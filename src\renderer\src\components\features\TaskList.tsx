import { useMemo, useState, useCallback } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  DragOverlay
} from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  ChevronDown,
  ChevronRight,
  Expand,
  Minimize,
  BarChart3
} from 'lucide-react'
import TaskItem from './TaskItem'
import EnhancedTaskItem from './EnhancedTaskItem'
import HierarchicalTaskItem from './HierarchicalTaskItem'
import VirtualizedTaskList from './VirtualizedTaskList'
import TaskAttributesDialog from './TaskAttributesDialog'
import type { ExtendedTask } from '../../store/taskStore'
import type { Task } from '../../../../shared/types'
import { startTimer } from '../../utils/performanceMonitor'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import { useTaskHierarchy } from '../../hooks/useTaskHierarchy'
import { useLanguage } from '../../contexts/LanguageContext'
import { cn } from '../../lib/utils'

interface TaskListProps {
  tasks: ExtendedTask[]
  onTaskToggle?: (taskId: string, completed: boolean) => void
  onTaskEdit?: (task: ExtendedTask) => void
  onTaskDelete?: (taskId: string) => void
  onTaskAddSubtask?: (parentId: string) => void
  onTaskMove?: (taskId: string, newParentId?: string, newIndex?: number) => void
  onTaskClick?: (task: ExtendedTask) => void // 新增：点击任务的回调
  className?: string
  useEnhancedView?: boolean // 是否使用增强视图
  useHierarchicalView?: boolean // 是否使用层级视图
  useVirtualizedView?: boolean // 新增：是否使用虚拟化视图（性能优化）
  showHierarchyControls?: boolean // 是否显示层级控制按钮
}

export function TaskList({
  tasks,
  onTaskToggle,
  onTaskEdit,
  onTaskDelete,
  onTaskAddSubtask,
  onTaskMove,
  onTaskClick,
  className,
  useEnhancedView = false,
  useHierarchicalView = false,
  useVirtualizedView = false,
  showHierarchyControls = false
}: TaskListProps) {
  const [activeId, setActiveId] = useState<string | null>(null)
  const [overId, setOverId] = useState<string | null>(null)
  const [dragIntent, setDragIntent] = useState<string | null>(null)
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [isAttributesDialogOpen, setIsAttributesDialogOpen] = useState(false)
  const { addNotification } = useUIStore()
  const { t } = useLanguage()

  // 层级管理 hook
  const {
    taskTree,
    visibleTasks,
    expandedTasks,
    stats,
    toggleExpand,
    expandAll,
    collapseAll,
    expandToLevel,
    isExpanded
  } = useTaskHierarchy({
    tasks,
    defaultExpanded: false,
    maxAutoExpandLevel: 2
  })

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8
      }
    })
  )

  // 使用 useTaskHierarchy hook 中的 taskTree，这里不需要重复定义

  // Get all task IDs for sortable context
  const taskIds = useMemo(() => tasks.map((task) => task.id), [tasks])

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const endTimer = startTimer('drag-start')
    setActiveId(event.active.id as string)
    setDragIntent('Drag vertically to reorder • horizontally to change level')
    endTimer()
  }, [])

  const handleDragOver = useCallback(
    (event: DragOverEvent) => {
      const endTimer = startTimer('drag-over')
      const { over, delta } = event
      setOverId(over ? (over.id as string) : null)

      // Detect drag intent based on horizontal offset
      if (over && activeId && delta) {
        const horizontalOffset = delta.x || 0
        const INDENT_THRESHOLD = 30

        const activeTask = tasks.find((task) => task.id === activeId)
        const overTask = tasks.find((task) => task.id === over.id)

        if (activeTask && overTask) {
          if (horizontalOffset > INDENT_THRESHOLD) {
            setDragIntent(`→ Make subtask of "${overTask.content}"`)
          } else if (horizontalOffset < -INDENT_THRESHOLD && activeTask.parentId) {
            setDragIntent(`← Promote to parent level`)
          } else {
            setDragIntent(`↕ Reorder at same level`)
          }
        }
      } else {
        setDragIntent(null)
      }

      endTimer()
    },
    [activeId, tasks]
  )

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const endTimer = startTimer('drag-end')
      const { active, over, delta } = event

      // Reset drag state immediately for better UX
      setActiveId(null)
      setOverId(null)
      setDragIntent(null)

      if (!over || active.id === over.id) {
        endTimer()
        return
      }

      const activeId = active.id as string
      const overId = over.id as string

      try {
        // Find the active and over tasks
        const activeTask = tasks.find((task) => task.id === activeId)
        const overTask = tasks.find((task) => task.id === overId)

        if (!activeTask || !overTask) {
          console.warn('Task not found during drag operation:', { activeId, overId })
          return
        }

        // Prevent circular dependency: can't move parent to its own child
        const isCircularDependency = (parentId: string, childId: string): boolean => {
          const checkTask = tasks.find((task) => task.id === childId)
          if (!checkTask) return false
          if (checkTask.parentId === parentId) return true
          if (checkTask.parentId) {
            return isCircularDependency(parentId, checkTask.parentId)
          }
          return false
        }

        if (isCircularDependency(activeId, overId)) {
          console.warn('Circular dependency detected, operation cancelled')
          addNotification({
            type: 'warning',
            title: 'Invalid move',
            message: 'Cannot move task: would create circular dependency'
          })
          return
        }

        // Determine drag intention based on horizontal offset
        const horizontalOffset = delta?.x || 0
        const INDENT_THRESHOLD = 30 // pixels to trigger child relationship

        let newParentId: string | undefined
        let newIndex: number | undefined
        let operation: string

        if (horizontalOffset > INDENT_THRESHOLD) {
          // Dragged significantly to the right -> make it a subtask
          newParentId = overId
          newIndex = 0
          operation = `"${activeTask.content}" → child of "${overTask.content}"`
        } else if (horizontalOffset < -INDENT_THRESHOLD && activeTask.parentId) {
          // Dragged significantly to the left -> move to parent level
          const parentTask = tasks.find((t) => t.id === activeTask.parentId)
          newParentId = parentTask?.parentId || undefined
          operation = `"${activeTask.content}" → promoted to ${newParentId ? 'parent level' : 'root level'}`
        } else {
          // Normal vertical drag -> reorder at same level
          newParentId = overTask.parentId || undefined
          // Calculate new index based on position relative to overTask
          const siblings = tasks.filter((t) => t.parentId === newParentId)
          const overIndex = siblings.findIndex((t) => t.id === overId)
          newIndex = overIndex
          operation = `"${activeTask.content}" → reordered ${newParentId ? 'within parent' : 'at root level'}`
        }

        console.log(`🎯 Drag operation: ${operation}`)
        onTaskMove?.(activeId, newParentId, newIndex)

        // 显示成功通知
        addNotification({
          type: 'success',
          title: 'Task moved',
          message: operation
        })
      } catch (error) {
        console.error('Error during drag operation:', error)
        addNotification({
          type: 'error',
          title: 'Move failed',
          message: 'Failed to move task. Please try again.'
        })
      } finally {
        endTimer()
      }
    },
    [tasks, onTaskMove, addNotification]
  )

  // Get active task for drag overlay
  const activeTask = activeId ? tasks.find((task) => task.id === activeId) : null

  // Enhanced task handlers
  const handleUpdateTaskStatus = useCallback(async (task: Task, newStatus: string) => {
    try {
      // First check if task exists in database
      console.log('Updating task status:', { taskId: task.id, newStatus })

      const updates: any = { status: newStatus }

      // Auto-set timestamps based on status
      if (newStatus === 'in_progress' && !task.startedAt) {
        updates.startedAt = new Date()
      }
      if (newStatus === 'done' && !task.completedAt) {
        updates.completedAt = new Date()
        updates.completed = true
      }

      const result = await databaseApi.updateTask({
        id: task.id,
        updates
      })

      if (result.success) {
        // Update local state through existing handler
        onTaskEdit?.(result.data)
        addNotification({
          type: 'success',
          title: 'Task updated',
          message: `Status changed to ${newStatus.replace('_', ' ')}`
        })
      } else {
        throw new Error(result.error || 'Failed to update task')
      }
    } catch (error) {
      console.error('Failed to update task status:', error)
      addNotification({
        type: 'error',
        title: 'Failed to update task',
        message: error instanceof Error ? error.message : 'Task may not exist in database. Please refresh the page.'
      })
    }
  }, [onTaskEdit, addNotification])

  const handleOpenTaskAttributes = useCallback((task: Task) => {
    setSelectedTask(task)
    setIsAttributesDialogOpen(true)
  }, [])

  const handleSaveTaskAttributes = useCallback(async (updates: any) => {
    if (!selectedTask) return

    try {
      const result = await databaseApi.updateTask({
        id: selectedTask.id,
        updates
      })

      if (result.success) {
        // Update local state through existing handler
        onTaskEdit?.(result.data)
        addNotification({
          type: 'success',
          title: 'Task attributes updated',
          message: 'Task properties have been saved successfully'
        })
      } else {
        throw new Error(result.error || 'Failed to update task')
      }
    } catch (error) {
      console.error('Failed to save task attributes:', error)
      addNotification({
        type: 'error',
        title: 'Failed to save attributes',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
      throw error
    }
  }, [selectedTask, onTaskEdit, addNotification])

  // Recursive render function with performance optimization
  const renderTaskTree = useCallback(
    (taskList: any[], level = 0) => {
      return taskList.map((task) => {
        const isDropTarget = overId === task.id && activeId !== task.id
        const isBeingDragged = activeId === task.id

        return (
          <div key={task.id} className={isDropTarget ? 'drop-target' : ''}>
            {useEnhancedView ? (
              <EnhancedTaskItem
                task={task as Task}
                level={level}
                onToggleComplete={(t) => onTaskToggle?.(t.id, !t.completed)}
                onUpdateStatus={handleUpdateTaskStatus}
                onOpenAttributes={handleOpenTaskAttributes}
                onDelete={onTaskDelete ? (t) => onTaskDelete(t.id) : undefined}
                showHierarchy={true}
              />
            ) : (
              <TaskItem
                task={task}
                level={level}
                onToggle={onTaskToggle}
                onEdit={onTaskEdit}
                onDelete={onTaskDelete}
                onAddSubtask={onTaskAddSubtask}
                isDropTarget={isDropTarget}
                isBeingDragged={isBeingDragged}
              />
            )}
            {task.children.length > 0 && (
              <div className={useEnhancedView ? 'ml-6 mt-2 space-y-2' : ''}>
                {renderTaskTree(
                  task.children,
                  level + 1
                )}
              </div>
            )}
          </div>
        )
      })
    },
    [activeId, overId, onTaskToggle, onTaskEdit, onTaskDelete, onTaskAddSubtask]
  )

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
        <div className={className}>
          {/* 层级控制工具栏 */}
          {showHierarchyControls && taskTree.length > 0 && (
            <div className="mb-4 p-3 bg-muted/30 rounded-lg border">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">{t('pages.projects.detail.tasks.hierarchyControls')}</span>
                  <Badge variant="secondary" className="text-xs">
                    {t('pages.projects.detail.tasks.hierarchyStats', { tasks: stats.totalTasks, levels: stats.maxDepth + 1 })}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={expandAll}
                    className="h-8 text-xs"
                  >
                    <Expand className="h-3 w-3 mr-1" />
                    {t('pages.projects.detail.tasks.expandAll')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={collapseAll}
                    className="h-8 text-xs"
                  >
                    <Minimize className="h-3 w-3 mr-1" />
                    {t('pages.projects.detail.tasks.collapseAll')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => expandToLevel(1)}
                    className="h-8 text-xs"
                  >
                    {t('pages.projects.detail.tasks.level1')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => expandToLevel(2)}
                    className="h-8 text-xs"
                  >
                    {t('pages.projects.detail.tasks.level2')}
                  </Button>
                </div>
              </div>

              {/* 统计信息 */}
              <div className="mt-2 flex items-center gap-4 text-xs text-muted-foreground">
                <span>{t('pages.projects.detail.tasks.completion')}: {stats.completionRate}%</span>
                <span>{t('pages.projects.detail.tasks.expanded')}: {stats.expandedCount}/{stats.tasksWithChildren}</span>
                <span>{t('pages.projects.detail.tasks.maxDepth')}: {stats.maxDepth + 1}</span>
              </div>
            </div>
          )}

          {taskTree.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-4xl mb-2">📝</div>
              <p className="text-sm">{t('pages.projects.detail.tasks.noTasks')}</p>
              <p className="text-xs mt-1">{t('pages.projects.detail.tasks.noTasksHint')}</p>
            </div>
          ) : useVirtualizedView ? (
            // 虚拟化视图（性能优化）
            <VirtualizedTaskList
              tasks={tasks}
              onTaskToggle={onTaskToggle}
              onTaskEdit={onTaskEdit}
              onTaskDelete={onTaskDelete}
              onTaskAddSubtask={onTaskAddSubtask}
              onTaskClick={onTaskClick}
              className="space-y-2"
            />
          ) : useHierarchicalView ? (
            // 新的层级视图
            <div className="space-y-2">
              {taskTree.map((task) => (
                <HierarchicalTaskItem
                  key={task.id}
                  task={task}
                  level={0}
                  onToggleComplete={(t) => onTaskToggle?.(t.id, !t.completed)}
                  onUpdateStatus={handleUpdateTaskStatus}
                  onOpenAttributes={handleOpenTaskAttributes}
                  onDelete={onTaskDelete ? (t) => onTaskDelete(t.id) : undefined}
                  onAddSubtask={onTaskAddSubtask}
                  onTaskClick={onTaskClick}
                  expandedTasks={expandedTasks}
                  onToggleExpand={toggleExpand}
                  showHierarchy={true}
                />
              ))}
            </div>
          ) : (
            // 原有的渲染逻辑
            renderTaskTree(taskTree)
          )}
        </div>
      </SortableContext>

      <DragOverlay>
        {activeTask ? (
          <div className="drag-overlay">
            <TaskItem
              task={activeTask}
              level={0}
              onToggle={() => {}}
              onEdit={() => {}}
              onDelete={() => {}}
              onAddSubtask={() => {}}
              isOverlay={true}
            />
            {/* Drag Intent Tooltip */}
            {dragIntent && (
              <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-3 py-2 rounded-lg shadow-lg whitespace-nowrap z-50">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  {dragIntent}
                </div>
                {/* Arrow pointing up */}
                <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
              </div>
            )}
          </div>
        ) : null}
      </DragOverlay>

      {/* Task Attributes Dialog */}
      <TaskAttributesDialog
        task={selectedTask}
        isOpen={isAttributesDialogOpen}
        onClose={() => {
          setIsAttributesDialogOpen(false)
          setSelectedTask(null)
        }}
        onSave={handleSaveTaskAttributes}
      />
    </DndContext>
  )
}

export default TaskList
